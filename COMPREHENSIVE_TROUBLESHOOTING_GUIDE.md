# Comprehensive Troubleshooting Guide - Nirvana Organics Test Environment

## 🚨 **Critical Issues Identified**

### **Issue 1: Environment Variables Not Loading**
**Symptoms**: DB_HOST, DB_PORT, SQUARE_ACCESS_TOKEN, EMAIL_USER showing as "undefined"
**Root Cause**: PM2 processes not properly loading environment files
**Impact**: Database connections fail, email authentication fails, Square payments disabled

### **Issue 2: Incorrect User Configuration**
**Symptoms**: `sudo: unknown user deploy` errors
**Root Cause**: Scripts configured for "deploy" user but actual user is "Nirvana"
**Impact**: Cannot manage PM2 processes or check logs

### **Issue 3: Admin Server Health Endpoint Not Responding**
**Symptoms**: Main server health endpoint works, admin server does not
**Root Cause**: Admin server not starting properly or Nginx routing issues
**Impact**: Admin panel functionality unavailable

## 🛠️ **Step-by-Step Solution**

### **Step 1: Fix User Configuration and Environment Loading**
```bash
# SSH into your server
ssh Nirvana@your-server

# Make scripts executable
chmod +x diagnose-pm2-env.sh fix-pm2-env-loading.sh fix-admin-health-endpoint.sh verify-fix.sh

# Run comprehensive PM2 environment fix
sudo ./fix-pm2-env-loading.sh
```

### **Step 2: Verify Environment Variable Loading**
```bash
# Test environment loading manually
cd /var/www/nirvana-backend-test

# Test main server environment
NODE_ENV=test node -e "
require('dotenv').config({ path: '.env.test' });
console.log('DB_HOST:', process.env.DB_HOST);
console.log('SQUARE_ACCESS_TOKEN:', process.env.SQUARE_ACCESS_TOKEN ? 'CONFIGURED' : 'MISSING');
console.log('EMAIL_USER:', process.env.EMAIL_USER);
"

# Test admin server environment
NODE_ENV=test node -e "
require('dotenv').config({ path: '.env.admin.test' });
console.log('DB_HOST:', process.env.DB_HOST);
console.log('PORT:', process.env.PORT);
"
```

### **Step 3: Fix Admin Server Issues**
```bash
# Run admin server diagnostic and fix
./fix-admin-health-endpoint.sh

# Check admin server logs
sudo -u Nirvana pm2 logs nirvana-backend-admin-test --lines 20

# Test health endpoints
./test-health.sh
```

### **Step 4: Verify All Fixes**
```bash
# Run comprehensive verification
./verify-fix.sh

# Check PM2 status
sudo -u Nirvana pm2 status

# Check recent logs for success indicators
sudo -u Nirvana pm2 logs --lines 30
```

## ✅ **Expected Results After Fix**

### **Environment Variables Should Load Correctly:**
```
✅ NODE_ENV: test
✅ DB_HOST: srv1921.hstgr.io
✅ DB_PORT: 3306
✅ SQUARE_ACCESS_TOKEN: CONFIGURED
✅ EMAIL_USER: <EMAIL>
```

### **PM2 Processes Should Show:**
```
┌─────┬──────────────────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id  │ name                         │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├─────┼──────────────────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0   │ nirvana-backend-main-test    │ default     │ 1.0.0   │ fork    │ 12345    │ 2m     │ 0    │ online    │ 0%       │ 50.0mb   │ Nirvana  │ disabled │
│ 1   │ nirvana-backend-admin-test   │ default     │ 1.0.0   │ fork    │ 12346    │ 2m     │ 0    │ online    │ 0%       │ 45.0mb   │ Nirvana  │ disabled │
└─────┴──────────────────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
```

### **Success Log Messages:**
```
✅ Mock email transporter ready for testing
✅ Mock orders email transporter ready for testing
✅ Mock support email transporter ready for testing
✅ Database connected successfully to srv1921.hstgr.io:3306
✅ Square SDK initialized successfully for environment: sandbox
🚀 Nirvana Organics server running on port 5000
🚀 Nirvana Organics Admin Panel server running on port 3001
```

### **Health Endpoints Should Respond:**
```bash
curl https://test.shopnirvanaorganics.com/api/health
# Should return: {"status":"ok","timestamp":"..."}

curl https://test.shopnirvanaorganics.com/admin/api/health
# Should return: {"status":"ok","timestamp":"..."}
```

## 🔧 **Key Technical Fixes Applied**

### **1. PM2 Ecosystem Configuration**
- **Fixed**: Created proper `ecosystem.config.js` with explicit `env_file` paths
- **Fixed**: Set correct user permissions and log file paths
- **Fixed**: Ensured NODE_ENV=test is set for both processes

### **2. Environment File Management**
- **Fixed**: Corrected file permissions (600) and ownership (Nirvana:Nirvana)
- **Fixed**: Ensured all required variables are present in both .env files
- **Fixed**: Set NODE_ENV=test to activate mock transporters

### **3. Server Configuration**
- **Fixed**: Updated server files to handle both 'test' and 'testing' NODE_ENV values
- **Fixed**: Ensured proper environment file loading in both main and admin servers
- **Fixed**: Verified health endpoints exist and are accessible

## 🚨 **If Issues Persist**

### **Environment Variables Still Undefined:**
```bash
# Check file permissions
ls -la /var/www/nirvana-backend-test/.env*

# Test manual loading
cd /var/www/nirvana-backend-test
node -e "console.log(require('fs').readFileSync('.env.test', 'utf8').split('\n').slice(0,10))"

# Restart PM2 completely
sudo -u Nirvana pm2 kill
sudo -u Nirvana pm2 start ecosystem.config.js
```

### **Admin Server Still Not Responding:**
```bash
# Check if process is actually running
sudo -u Nirvana pm2 show nirvana-backend-admin-test

# Check what's listening on port 3001
netstat -tlnp | grep :3001

# Test direct connection
curl -v http://localhost:3001/api/health

# Check Nginx configuration
sudo nginx -t
sudo systemctl reload nginx
```

### **Database Connection Issues:**
```bash
# Test database connection manually
mysql -h srv1921.hstgr.io -P 3306 -u u106832845_root -p u106832845_nirvana

# Check if server is trying to connect to localhost instead
sudo -u Nirvana pm2 logs | grep -E "(localhost|127.0.0.1|::1)"
```

## 📞 **Support Commands**

### **Monitoring:**
```bash
# Watch PM2 logs in real-time
sudo -u Nirvana pm2 logs --lines 0

# Monitor specific process
sudo -u Nirvana pm2 monit

# Check system resources
htop
```

### **Debugging:**
```bash
# Enable debug mode
sudo -u Nirvana pm2 restart all --update-env

# Check environment variables in running process
sudo -u Nirvana pm2 show nirvana-backend-main-test | grep -A 20 "env:"

# Test health endpoints
watch -n 5 './test-health.sh'
```

## 🎯 **Success Criteria**

✅ **All environment variables load correctly (no "undefined" values)**
✅ **Both PM2 processes show "online" status**
✅ **Database connects to srv1921.hstgr.io:3306 (not localhost)**
✅ **Email services use mock transporters (no SMTP errors)**
✅ **Square SDK initializes with sandbox token**
✅ **Both health endpoints respond successfully**
✅ **No sudo permission errors with user "Nirvana"**

Your test environment should be fully functional after applying these fixes! 🎉
