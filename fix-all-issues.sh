#!/bin/bash

# Comprehensive Fix Script for Nirvana Organics Test Environment
# Addresses: Database connection, Email authentication, Square configuration

echo "🔧 Comprehensive Fix for Nirvana Organics Test Environment"
echo "=========================================================="

# Check if running as root or with sudo
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root or with sudo"
   exit 1
fi

# Set variables
DEPLOY_DIR="/var/www/nirvana-backend-test"
DEPLOY_USER="Nirvana"

echo "📍 Working directory: $DEPLOY_DIR"
echo "👤 Deploy user: $DEPLOY_USER"
echo ""

# Step 1: Stop PM2 processes
echo "🛑 Stopping PM2 processes..."
sudo -u $DEPLOY_USER pm2 stop nirvana-backend-main-test nirvana-backend-admin-test 2>/dev/null || echo "Processes not running"
sleep 2

# Step 2: Backup existing environment files
echo "📦 Creating backups..."
BACKUP_DIR="$DEPLOY_DIR/backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp "$DEPLOY_DIR/.env.test" "$BACKUP_DIR/" 2>/dev/null || echo "No .env.test to backup"
cp "$DEPLOY_DIR/.env.admin.test" "$BACKUP_DIR/" 2>/dev/null || echo "No .env.admin.test to backup"
cp "$DEPLOY_DIR/ecosystem.config.test.js" "$BACKUP_DIR/" 2>/dev/null || echo "No ecosystem config to backup"

# Step 3: Create corrected .env.test file
echo "📝 Creating corrected .env.test..."
cat > "$DEPLOY_DIR/.env.test" << 'EOF'
# Nirvana Organics - Main Server Test Configuration
NODE_ENV=test
PORT=5000
FRONTEND_URL=https://test.shopnirvanaorganics.com
BACKEND_URL=https://test.shopnirvanaorganics.com

# Database Configuration (Test)
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_DIALECT=mysql

# Security Configuration (Test)
JWT_SECRET=test-jwt-secret-key-for-development-only-not-for-production-use-minimum-64-characters
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=10

# CORS Configuration (Test)
CORS_ORIGIN=https://test.shopnirvanaorganics.com
CORS_CREDENTIALS=true

# Session Configuration (Test)
SESSION_SECRET=test-session-secret-for-development-only-not-for-production-use-minimum-64-characters
SESSION_MAX_AGE=86400000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
MAIN_RATE_LIMIT_MAX_REQUESTS=1000

# Email Configuration (Test)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics

# Email Addresses (Test Environment)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=wiba ieco dxth xdyn
EMAIL_ORDERS_HOST=smtp.gmail.com
EMAIL_ORDERS_PORT=587

EMAIL_SUPPORT=<EMAIL>
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=gqfh cyls ryuv yind
EMAIL_SUPPORT_HOST=smtp.gmail.com
EMAIL_SUPPORT_PORT=587

EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_NO_REPLY=<EMAIL>
EMAIL_ADMIN=<EMAIL>

# VAPID Configuration (Test)
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>

# Payment Configuration (Test/Sandbox)
SQUARE_ENVIRONMENT=sandbox
SQUARE_APPLICATION_ID=sandbox-sq0idb-iKZkLt1rHUkKu9X4L7LEtA
SQUARE_ACCESS_TOKEN=****************************************************************

# Google OAuth Configuration (Test)
GOOGLE_CLIENT_ID=your-test-google-client-id
GOOGLE_CLIENT_SECRET=your-test-google-client-secret
GOOGLE_REDIRECT_URI=https://test.shopnirvanaorganics.com/auth/google/callback

# Square OAuth Configuration (Test)
SQUARE_OAUTH_CLIENT_ID=your-test-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-test-square-oauth-client-secret
SQUARE_OAUTH_REDIRECT_URI=https://test.shopnirvanaorganics.com/auth/square/callback

# File Upload Configuration (Test)
UPLOAD_PATH=./public/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Cache Configuration (Test)
CACHE_TTL=300
REDIS_URL=redis://localhost:6379

# Logging Configuration (Test)
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# Performance Monitoring (Test)
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_THRESHOLD=1000

# Feature Flags (Test)
ENABLE_EMAIL_VERIFICATION=false
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=false

# API Configuration (Test)
API_VERSION=v1
API_RATE_LIMIT=1000
API_TIMEOUT=30000

# Security Headers (Test)
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# Development/Debug (Test)
DEBUG_MODE=true
ENABLE_SWAGGER=true
ENABLE_MORGAN_LOGGING=true

# Test Environment Specific
TEST_MODE=true
MOCK_EXTERNAL_APIS=true
DISABLE_EMAIL_SENDING=true
EOF

# Step 4: Create corrected .env.admin.test file
echo "📝 Creating corrected .env.admin.test..."
cat > "$DEPLOY_DIR/.env.admin.test" << 'EOF'
# Nirvana Organics - Admin Server Test Configuration
NODE_ENV=test
PORT=3001
FRONTEND_URL=https://test.shopnirvanaorganics.com/admin
BACKEND_URL=https://test.shopnirvanaorganics.com

# Database Configuration (Test)
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_DIALECT=mysql

# Security Configuration (Test)
JWT_SECRET=test-admin-jwt-secret-key-for-development-only-not-for-production-use-minimum-64-characters
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=10

# CORS Configuration (Test)
CORS_ORIGIN=https://test.shopnirvanaorganics.com
CORS_CREDENTIALS=true

# Session Configuration (Test)
SESSION_SECRET=test-admin-session-secret-for-development-only-not-for-production-use-minimum-64-characters
SESSION_MAX_AGE=86400000

# Rate Limiting (Admin - More Restrictive)
RATE_LIMIT_WINDOW_MS=900000
ADMIN_RATE_LIMIT_MAX_REQUESTS=500

# Admin Security (Test)
ADMIN_SECURITY_MODE=true
ADMIN_IP_WHITELIST=

# Email Configuration (Test admin)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics

# Email Addresses (Test Admin Environment)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=wiba ieco dxth xdyn
EMAIL_ORDERS_HOST=smtp.gmail.com
EMAIL_ORDERS_PORT=587

EMAIL_SUPPORT=<EMAIL>
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=gqfh cyls ryuv yind
EMAIL_SUPPORT_HOST=smtp.gmail.com
EMAIL_SUPPORT_PORT=587

EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_NO_REPLY=<EMAIL>
EMAIL_ADMIN=<EMAIL>

# VAPID Configuration (Test Admin)
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>

# Payment Configuration (Test/Sandbox)
SQUARE_ENVIRONMENT=sandbox
SQUARE_APPLICATION_ID=sandbox-sq0idb-iKZkLt1rHUkKu9X4L7LEtA
SQUARE_ACCESS_TOKEN=****************************************************************

# Google OAuth Configuration (Test Admin)
GOOGLE_CLIENT_ID=your-test-google-client-id
GOOGLE_CLIENT_SECRET=your-test-google-client-secret
GOOGLE_REDIRECT_URI=https://test.shopnirvanaorganics.com/admin/auth/google/callback

# Square OAuth Configuration (Test Admin)
SQUARE_OAUTH_CLIENT_ID=your-test-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-test-square-oauth-client-secret
SQUARE_OAUTH_REDIRECT_URI=https://test.shopnirvanaorganics.com/admin/auth/square/callback

# File Upload Configuration (Test Admin)
UPLOAD_PATH=./public/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Cache Configuration (Test Admin)
CACHE_TTL=300
REDIS_URL=redis://localhost:6379

# Logging Configuration (Test Admin)
LOG_LEVEL=debug
LOG_FILE=./logs/admin.log

# Performance Monitoring (Test Admin)
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_THRESHOLD=1000

# Feature Flags (Test Admin)
ENABLE_EMAIL_VERIFICATION=false
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=false

# API Configuration (Test Admin)
API_VERSION=v1
API_RATE_LIMIT=500
API_TIMEOUT=30000

# Security Headers (Test Admin)
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# Development/Debug (Test Admin)
DEBUG_MODE=true
ENABLE_SWAGGER=true
ENABLE_MORGAN_LOGGING=true

# Test Environment Specific (Admin)
TEST_MODE=true
MOCK_EXTERNAL_APIS=true
DISABLE_EMAIL_SENDING=true
ADMIN_PANEL_MODE=true
EOF

# Step 5: Set proper file permissions
echo "🔒 Setting file permissions..."
chown $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_DIR/.env.test"
chown $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_DIR/.env.admin.test"
chmod 600 "$DEPLOY_DIR/.env.test"
chmod 600 "$DEPLOY_DIR/.env.admin.test"

# Step 6: Update server files for proper environment loading
echo "🔧 Updating server files..."

# Update main server file
if [ -f "$DEPLOY_DIR/server/index.js" ]; then
    # Create backup
    cp "$DEPLOY_DIR/server/index.js" "$BACKUP_DIR/index.js.backup"

    # Update environment loading logic
    sed -i '1,10s/NODE_ENV === '\''testing'\''/NODE_ENV === '\''test'\'' || process.env.NODE_ENV === '\''testing'\''/g' "$DEPLOY_DIR/server/index.js"
    echo "✅ Updated main server environment loading"
else
    echo "⚠️  Main server file not found at $DEPLOY_DIR/server/index.js"
fi

# Update admin server file
if [ -f "$DEPLOY_DIR/server/admin-server.js" ]; then
    # Create backup
    cp "$DEPLOY_DIR/server/admin-server.js" "$BACKUP_DIR/admin-server.js.backup"

    # Update environment loading logic
    sed -i '1,10s/NODE_ENV === '\''testing'\''/NODE_ENV === '\''test'\'' || process.env.NODE_ENV === '\''testing'\''/g' "$DEPLOY_DIR/server/admin-server.js"
    echo "✅ Updated admin server environment loading"
else
    echo "⚠️  Admin server file not found at $DEPLOY_DIR/server/admin-server.js"
fi

# Step 7: Clear PM2 cache and restart processes
echo "🔄 Restarting PM2 processes..."
sudo -u $DEPLOY_USER pm2 delete nirvana-backend-main-test nirvana-backend-admin-test 2>/dev/null || echo "Processes not found"
sleep 2

# Start processes fresh
cd "$DEPLOY_DIR"
if [ -f "ecosystem.config.test.js" ]; then
    sudo -u $DEPLOY_USER pm2 start ecosystem.config.test.js
else
    echo "⚠️  Starting processes manually..."
    sudo -u $DEPLOY_USER pm2 start server/index.js --name nirvana-backend-main-test
    sudo -u $DEPLOY_USER pm2 start server/admin-server.js --name nirvana-backend-admin-test
fi

# Step 8: Wait for startup and check status
echo "⏳ Waiting for processes to start..."
sleep 5

echo "📊 PM2 Status:"
sudo -u $DEPLOY_USER pm2 status

echo ""
echo "✅ Fix completed! Checking results..."
echo ""

# Step 9: Verify the fix
echo "🔍 Verification Results:"
echo "======================="

# Check recent logs for success indicators
echo "Main server logs (last 10 lines):"
sudo -u $DEPLOY_USER pm2 logs nirvana-backend-main-test --lines 10 | tail -10

echo ""
echo "Admin server logs (last 10 lines):"
sudo -u $DEPLOY_USER pm2 logs nirvana-backend-admin-test --lines 10 | tail -10

echo ""
echo "🎯 Expected Success Indicators:"
echo "✅ Mock email transporter ready for testing"
echo "✅ Database connected successfully"
echo "✅ Square SDK initialized successfully"
echo "✅ Server running on port 5000/3001"

echo ""
echo "📋 Manual Verification Commands:"
echo "sudo -u $DEPLOY_USER pm2 logs nirvana-backend-main-test --lines 20"
echo "sudo -u $DEPLOY_USER pm2 logs nirvana-backend-admin-test --lines 20"
echo "curl https://test.shopnirvanaorganics.com/api/health"
echo "curl https://test.shopnirvanaorganics.com/admin/api/health"
