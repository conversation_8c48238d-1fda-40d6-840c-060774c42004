#!/bin/bash

# Fix Environment Files Script for Nirvana Organics Test Environment
# Run this script on your server to fix the environment configuration

echo "🔧 Fixing Nirvana Organics Test Environment Configuration..."

# Backup existing files
echo "📦 Creating backups..."
cp /var/www/nirvana-backend-test/.env.test /var/www/nirvana-backend-test/.env.test.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "No .env.test to backup"
cp /var/www/nirvana-backend-test/.env.admin.test /var/www/nirvana-backend-test/.env.admin.test.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "No .env.admin.test to backup"

# Create corrected .env.test file
echo "📝 Creating corrected .env.test..."
cat > /var/www/nirvana-backend-test/.env.test << 'EOF'
# Nirvana Organics - Main Server Test Configuration
# Main Server Test Environment
# Deploy to test.shopnirvanaorganics.com FIRST for validation
# DO NOT use production values here

# Environment
NODE_ENV=test

# Server Configuration
PORT=5000
FRONTEND_URL=https://test.shopnirvanaorganics.com
BACKEND_URL=https://test.shopnirvanaorganics.com

# Database Configuration (Test)
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_DIALECT=mysql

# Security Configuration (Test)
JWT_SECRET=test-jwt-secret-key-for-development-only-not-for-production-use-minimum-64-characters
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=10

# CORS Configuration (Test)
CORS_ORIGIN=https://test.shopnirvanaorganics.com
CORS_CREDENTIALS=true

# Session Configuration (Test)
SESSION_SECRET=test-session-secret-for-development-only-not-for-production-use-minimum-64-characters
SESSION_MAX_AGE=86400000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
MAIN_RATE_LIMIT_MAX_REQUESTS=1000

# Email Configuration (Test)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics

# Email Addresses (Test Environment)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=wiba ieco dxth xdyn
EMAIL_ORDERS_HOST=smtp.gmail.com
EMAIL_ORDERS_PORT=587

EMAIL_SUPPORT=<EMAIL>
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=gqfh cyls ryuv yind
EMAIL_SUPPORT_HOST=smtp.gmail.com
EMAIL_SUPPORT_PORT=587

EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_NO_REPLY=<EMAIL>
EMAIL_ADMIN=<EMAIL>

# VAPID Configuration (Test)
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>

# Payment Configuration (Test/Sandbox)
SQUARE_ENVIRONMENT=sandbox
SQUARE_APPLICATION_ID=sandbox-sq0idb-iKZkLt1rHUkKu9X4L7LEtA
SQUARE_ACCESS_TOKEN=****************************************************************

# Google OAuth Configuration (Test)
GOOGLE_CLIENT_ID=your-test-google-client-id
GOOGLE_CLIENT_SECRET=your-test-google-client-secret
GOOGLE_REDIRECT_URI=https://test.shopnirvanaorganics.com/auth/google/callback

# Square OAuth Configuration (Test)
SQUARE_OAUTH_CLIENT_ID=your-test-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-test-square-oauth-client-secret
SQUARE_OAUTH_REDIRECT_URI=https://test.shopnirvanaorganics.com/auth/square/callback

# File Upload Configuration (Test)
UPLOAD_PATH=./public/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Cache Configuration (Test)
CACHE_TTL=300
REDIS_URL=redis://localhost:6379

# Logging Configuration (Test)
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# Performance Monitoring (Test)
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_THRESHOLD=1000

# Feature Flags (Test)
ENABLE_EMAIL_VERIFICATION=false
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=false

# API Configuration (Test)
API_VERSION=v1
API_RATE_LIMIT=1000
API_TIMEOUT=30000

# Security Headers (Test)
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# Development/Debug (Test)
DEBUG_MODE=true
ENABLE_SWAGGER=true
ENABLE_MORGAN_LOGGING=true

# Test Environment Specific
TEST_MODE=true
MOCK_EXTERNAL_APIS=true
DISABLE_EMAIL_SENDING=true
EOF

# Create corrected .env.admin.test file
echo "📝 Creating corrected .env.admin.test..."
cat > /var/www/nirvana-backend-test/.env.admin.test << 'EOF'
# Nirvana Organics - Admin Server Test Configuration
# Admin Panel Test Environment

# Environment
NODE_ENV=test

# Server Configuration
PORT=3001
FRONTEND_URL=https://test.shopnirvanaorganics.com/admin
BACKEND_URL=https://test.shopnirvanaorganics.com

# Database Configuration (Test)
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_DIALECT=mysql

# Security Configuration (Test)
JWT_SECRET=test-admin-jwt-secret-key-for-development-only-not-for-production-use-minimum-64-characters
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=10

# CORS Configuration (Test)
CORS_ORIGIN=https://test.shopnirvanaorganics.com
CORS_CREDENTIALS=true

# Session Configuration (Test)
SESSION_SECRET=test-admin-session-secret-for-development-only-not-for-production-use-minimum-64-characters
SESSION_MAX_AGE=86400000

# Rate Limiting (Admin - More Restrictive)
RATE_LIMIT_WINDOW_MS=900000
ADMIN_RATE_LIMIT_MAX_REQUESTS=500

# Admin Security (Test)
ADMIN_SECURITY_MODE=true
ADMIN_IP_WHITELIST=

# Email Configuration (Test admin)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics

# Email Addresses (Test Admin Environment)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=wiba ieco dxth xdyn
EMAIL_ORDERS_HOST=smtp.gmail.com
EMAIL_ORDERS_PORT=587

EMAIL_SUPPORT=<EMAIL>
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=gqfh cyls ryuv yind
EMAIL_SUPPORT_HOST=smtp.gmail.com
EMAIL_SUPPORT_PORT=587

EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_NO_REPLY=<EMAIL>
EMAIL_ADMIN=<EMAIL>

# VAPID Configuration (Test Admin)
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>

# Payment Configuration (Test/Sandbox)
SQUARE_ENVIRONMENT=sandbox
SQUARE_APPLICATION_ID=sandbox-sq0idb-iKZkLt1rHUkKu9X4L7LEtA
SQUARE_ACCESS_TOKEN=****************************************************************

# Google OAuth Configuration (Test Admin)
GOOGLE_CLIENT_ID=your-test-google-client-id
GOOGLE_CLIENT_SECRET=your-test-google-client-secret
GOOGLE_REDIRECT_URI=https://test.shopnirvanaorganics.com/admin/auth/google/callback

# Square OAuth Configuration (Test Admin)
SQUARE_OAUTH_CLIENT_ID=your-test-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-test-square-oauth-client-secret
SQUARE_OAUTH_REDIRECT_URI=https://test.shopnirvanaorganics.com/admin/auth/square/callback

# File Upload Configuration (Test Admin)
UPLOAD_PATH=./public/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Cache Configuration (Test Admin)
CACHE_TTL=300
REDIS_URL=redis://localhost:6379

# Logging Configuration (Test Admin)
LOG_LEVEL=debug
LOG_FILE=./logs/admin.log

# Performance Monitoring (Test Admin)
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_THRESHOLD=1000

# Feature Flags (Test Admin)
ENABLE_EMAIL_VERIFICATION=false
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=false

# API Configuration (Test Admin)
API_VERSION=v1
API_RATE_LIMIT=500
API_TIMEOUT=30000

# Security Headers (Test Admin)
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# Development/Debug (Test Admin)
DEBUG_MODE=true
ENABLE_SWAGGER=true
ENABLE_MORGAN_LOGGING=true

# Test Environment Specific (Admin)
TEST_MODE=true
MOCK_EXTERNAL_APIS=true
DISABLE_EMAIL_SENDING=true
ADMIN_PANEL_MODE=true
EOF

# Set proper permissions
echo "🔒 Setting file permissions..."
chown deploy:deploy /var/www/nirvana-backend-test/.env.test
chown deploy:deploy /var/www/nirvana-backend-test/.env.admin.test
chmod 600 /var/www/nirvana-backend-test/.env.test
chmod 600 /var/www/nirvana-backend-test/.env.admin.test

# Restart PM2 processes
echo "🔄 Restarting PM2 processes..."
sudo -u deploy pm2 restart nirvana-backend-main-test
sudo -u deploy pm2 restart nirvana-backend-admin-test

echo "✅ Environment files have been fixed and PM2 processes restarted!"
echo ""
echo "🔍 Verification commands:"
echo "sudo -u deploy pm2 logs nirvana-backend-main-test --lines 20"
echo "sudo -u deploy pm2 logs nirvana-backend-admin-test --lines 20"
echo "sudo -u deploy pm2 status"
