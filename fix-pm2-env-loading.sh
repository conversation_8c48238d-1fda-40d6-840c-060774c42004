#!/bin/bash

# Comprehensive PM2 Environment Loading Fix for Nirvana Organics
# Addresses the core issue: PM2 processes not loading environment variables

echo "🔧 Fixing PM2 Environment Variable Loading Issues"
echo "================================================="

# Check if running as root or with sudo
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root or with sudo"
   exit 1
fi

# Set variables
DEPLOY_DIR="/var/www/nirvana-backend-test"
DEPLOY_USER="Nirvana"

echo "📍 Working directory: $DEPLOY_DIR"
echo "👤 User: $DEPLOY_USER"
echo ""

# Step 1: Diagnose current PM2 state
echo "🔍 Diagnosing current PM2 state..."
echo "Current PM2 processes for user $DEPLOY_USER:"
sudo -u $DEPLOY_USER pm2 list 2>/dev/null || echo "No PM2 processes found or PM2 not initialized"
echo ""

# Step 2: Stop all PM2 processes
echo "🛑 Stopping all PM2 processes..."
sudo -u $DEPLOY_USER pm2 stop all 2>/dev/null || echo "No processes to stop"
sudo -u $DEPLOY_USER pm2 delete all 2>/dev/null || echo "No processes to delete"
sleep 2

# Step 3: Check and fix environment files
echo "📁 Checking environment files..."
if [ ! -f "$DEPLOY_DIR/.env.test" ]; then
    echo "❌ .env.test not found! Creating it..."
    # Create the file (content from previous script)
    cat > "$DEPLOY_DIR/.env.test" << 'EOF'
NODE_ENV=test
PORT=5000
FRONTEND_URL=https://test.shopnirvanaorganics.com
BACKEND_URL=https://test.shopnirvanaorganics.com
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_DIALECT=mysql
JWT_SECRET=test-jwt-secret-key-for-development-only-not-for-production-use-minimum-64-characters
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=10
CORS_ORIGIN=https://test.shopnirvanaorganics.com
CORS_CREDENTIALS=true
SESSION_SECRET=test-session-secret-for-development-only-not-for-production-use-minimum-64-characters
SESSION_MAX_AGE=86400000
RATE_LIMIT_WINDOW_MS=900000
MAIN_RATE_LIMIT_MAX_REQUESTS=1000
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=wiba ieco dxth xdyn
EMAIL_ORDERS_HOST=smtp.gmail.com
EMAIL_ORDERS_PORT=587
EMAIL_SUPPORT=<EMAIL>
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=gqfh cyls ryuv yind
EMAIL_SUPPORT_HOST=smtp.gmail.com
EMAIL_SUPPORT_PORT=587
EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_NO_REPLY=<EMAIL>
EMAIL_ADMIN=<EMAIL>
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>
SQUARE_ENVIRONMENT=sandbox
SQUARE_APPLICATION_ID=sandbox-sq0idb-iKZkLt1rHUkKu9X4L7LEtA
SQUARE_ACCESS_TOKEN=****************************************************************
GOOGLE_CLIENT_ID=your-test-google-client-id
GOOGLE_CLIENT_SECRET=your-test-google-client-secret
GOOGLE_REDIRECT_URI=https://test.shopnirvanaorganics.com/auth/google/callback
SQUARE_OAUTH_CLIENT_ID=your-test-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-test-square-oauth-client-secret
SQUARE_OAUTH_REDIRECT_URI=https://test.shopnirvanaorganics.com/auth/square/callback
UPLOAD_PATH=./public/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx
CACHE_TTL=300
REDIS_URL=redis://localhost:6379
LOG_LEVEL=debug
LOG_FILE=./logs/app.log
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_THRESHOLD=1000
ENABLE_EMAIL_VERIFICATION=false
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=false
API_VERSION=v1
API_RATE_LIMIT=1000
API_TIMEOUT=30000
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true
DEBUG_MODE=true
ENABLE_SWAGGER=true
ENABLE_MORGAN_LOGGING=true
TEST_MODE=true
MOCK_EXTERNAL_APIS=true
DISABLE_EMAIL_SENDING=true
EOF
else
    echo "✅ .env.test exists"
fi

if [ ! -f "$DEPLOY_DIR/.env.admin.test" ]; then
    echo "❌ .env.admin.test not found! Creating it..."
    # Create admin env file (similar content but for admin)
    cp "$DEPLOY_DIR/.env.test" "$DEPLOY_DIR/.env.admin.test"
    sed -i 's/PORT=5000/PORT=3001/' "$DEPLOY_DIR/.env.admin.test"
    sed -i 's|FRONTEND_URL=https://test.shopnirvanaorganics.com|FRONTEND_URL=https://test.shopnirvanaorganics.com/admin|' "$DEPLOY_DIR/.env.admin.test"
    echo "ADMIN_PANEL_MODE=true" >> "$DEPLOY_DIR/.env.admin.test"
else
    echo "✅ .env.admin.test exists"
fi

# Step 4: Set proper permissions
echo "🔒 Setting file permissions..."
chown $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_DIR/.env.test"
chown $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_DIR/.env.admin.test"
chmod 600 "$DEPLOY_DIR/.env.test"
chmod 600 "$DEPLOY_DIR/.env.admin.test"

# Step 5: Create a simple PM2 ecosystem config that properly loads env files
echo "📝 Creating PM2 ecosystem configuration..."
cat > "$DEPLOY_DIR/ecosystem.config.js" << EOF
module.exports = {
  apps: [
    {
      name: 'nirvana-backend-main-test',
      script: './server/index.js',
      cwd: '$DEPLOY_DIR',
      env: {
        NODE_ENV: 'test'
      },
      env_file: '$DEPLOY_DIR/.env.test',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/.pm2/logs/nirvana-backend-main-test-error.log',
      out_file: '/home/<USER>/.pm2/logs/nirvana-backend-main-test-out.log',
      log_file: '/home/<USER>/.pm2/logs/nirvana-backend-main-test.log',
      time: true
    },
    {
      name: 'nirvana-backend-admin-test',
      script: './server/admin-server.js',
      cwd: '$DEPLOY_DIR',
      env: {
        NODE_ENV: 'test'
      },
      env_file: '$DEPLOY_DIR/.env.admin.test',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/.pm2/logs/nirvana-backend-admin-test-error.log',
      out_file: '/home/<USER>/.pm2/logs/nirvana-backend-admin-test-out.log',
      log_file: '/home/<USER>/.pm2/logs/nirvana-backend-admin-test.log',
      time: true
    }
  ]
};
EOF

chown $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_DIR/ecosystem.config.js"

# Step 6: Test environment loading manually
echo "🧪 Testing environment file loading..."
cd "$DEPLOY_DIR"

echo "Testing .env.test loading:"
sudo -u $DEPLOY_USER NODE_ENV=test node -e "
require('dotenv').config({ path: '.env.test' });
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('SQUARE_ACCESS_TOKEN:', process.env.SQUARE_ACCESS_TOKEN ? 'CONFIGURED' : 'MISSING');
console.log('EMAIL_USER:', process.env.EMAIL_USER);
" 2>/dev/null || echo "❌ Failed to test environment loading"

echo ""

# Step 7: Start PM2 processes with explicit environment loading
echo "🚀 Starting PM2 processes..."
cd "$DEPLOY_DIR"

# Start main server
echo "Starting main server..."
sudo -u $DEPLOY_USER pm2 start ecosystem.config.js --only nirvana-backend-main-test

# Start admin server
echo "Starting admin server..."
sudo -u $DEPLOY_USER pm2 start ecosystem.config.js --only nirvana-backend-admin-test

# Step 8: Wait and check status
echo "⏳ Waiting for processes to start..."
sleep 5

echo "📊 PM2 Status:"
sudo -u $DEPLOY_USER pm2 status

echo ""
echo "✅ PM2 Environment Loading Fix Complete!"
echo ""
echo "🔍 Verification commands:"
echo "sudo -u $DEPLOY_USER pm2 logs nirvana-backend-main-test --lines 20"
echo "sudo -u $DEPLOY_USER pm2 logs nirvana-backend-admin-test --lines 20"
echo "./verify-fix.sh"
