# Nirvana Organics - Production Deployment

This folder contains the complete production deployment package for Nirvana Organics e-commerce platform.

## 📁 Folder Contents

```
nirvana-production/
├── server/                          # Complete backend application
│   ├── index.js                     # Main server entry point
│   ├── admin-server.js              # Admin server entry point
│   ├── config/                      # Configuration files
│   ├── controllers/                 # API controllers
│   ├── middleware/                  # Express middleware
│   ├── models/                      # Database models
│   ├── routes/                      # API routes
│   ├── services/                    # Business logic services
│   ├── utils/                       # Utility functions
│   ├── migrations/                  # Database migrations
│   ├── seeders/                     # Database seeders
│   └── templates/                   # Email templates
├── dist/                            # Main frontend build (production)
├── dist-admin/                      # Admin frontend build (production)
├── .env.production                  # Main server environment config
├── .env.admin                       # Admin server environment config
├── ecosystem.config.production.js   # PM2 configuration
├── package.json                     # Node.js dependencies
├── package-lock.json               # Dependency lock file
├── deploy.sh                        # Deployment script
└── README.md                        # This file
```

## 🚀 Deployment Instructions

### Prerequisites

1. **Server Requirements:**
   - Ubuntu 20.04+ or CentOS 8+
   - Node.js 18+ and npm 9+
   - PM2 process manager
   - Nginx web server
   - SSL certificates for shopnirvanaorganics.com and admin.shopnirvanaorganics.com

2. **Database:**
   - MySQL 8.0+ or MariaDB 10.6+
   - Database and user credentials configured

3. **Domain Configuration:**
   - `shopnirvanaorganics.com` → Main customer site
   - `admin.shopnirvanaorganics.com` → Admin panel

### Step 1: Configure Environment Variables

**Edit `.env.production`:**
```bash
# Update these values with your production credentials
DB_HOST=your-production-db-host
DB_NAME=your-production-db-name
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password

JWT_SECRET=your-super-secure-jwt-secret-key-here-64-characters-minimum
SQUARE_ACCESS_TOKEN=your-production-square-access-token
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
# ... (see file for all required variables)
```

**Edit `.env.admin`:**
```bash
# Update these values with your admin production credentials
DB_HOST=your-production-db-host
DB_NAME=your-production-db-name
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password

JWT_SECRET=your-super-secure-admin-jwt-secret-key-here-64-characters
ADMIN_IP_WHITELIST=your-admin-ip-addresses-comma-separated
# ... (see file for all required variables)
```

### Step 2: Upload to Server

```bash
# Upload the entire nirvana-production folder to your server
scp -r nirvana-production/ user@your-server:/tmp/

# Or use rsync for better performance
rsync -avz --progress nirvana-production/ user@your-server:/tmp/nirvana-production/
```

### Step 3: Run Deployment Script

```bash
# SSH into your server
ssh user@your-server

# Navigate to deployment folder
cd /tmp/nirvana-production

# Make deployment script executable
chmod +x deploy.sh

# Run deployment (requires sudo access)
sudo ./deploy.sh
```

### Step 4: Configure Nginx

Create nginx configuration for production:

```nginx
# /etc/nginx/sites-available/nirvana-organics-production
server {
    listen 80;
    server_name shopnirvanaorganics.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name shopnirvanaorganics.com;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    root /var/www/nirvana-frontend/main;
    index index.html;
    
    # API proxy to backend
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Frontend SPA fallback
    location / {
        try_files $uri $uri/ /index.html;
    }
}

# Admin subdomain
server {
    listen 443 ssl http2;
    server_name admin.shopnirvanaorganics.com;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    root /var/www/nirvana-frontend/admin;
    index admin.html;
    
    # API proxy to admin backend
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Admin SPA fallback
    location / {
        try_files $uri $uri/ /admin.html;
    }
}
```

Enable the configuration:
```bash
sudo ln -s /etc/nginx/sites-available/nirvana-organics-production /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔍 Verification

After deployment, verify everything is working:

```bash
# Check PM2 processes
sudo -u deploy pm2 status

# Check logs
sudo -u deploy pm2 logs

# Test health endpoints
curl https://shopnirvanaorganics.com/api/health
curl https://admin.shopnirvanaorganics.com/api/health

# Test frontend
curl -I https://shopnirvanaorganics.com
curl -I https://admin.shopnirvanaorganics.com
```

## 🔧 Management Commands

```bash
# Restart services
sudo -u deploy pm2 restart all

# View logs
sudo -u deploy pm2 logs

# Monitor processes
sudo -u deploy pm2 monit

# Stop services
sudo -u deploy pm2 stop all

# Start services
sudo -u deploy pm2 start ecosystem.config.production.js --env production
```

## 🛡️ Security Notes

1. **Environment Files:** Never commit `.env.production` or `.env.admin` to version control
2. **File Permissions:** Ensure environment files have restricted permissions (600)
3. **Admin Access:** Configure `ADMIN_IP_WHITELIST` to restrict admin panel access
4. **SSL/TLS:** Always use HTTPS in production
5. **Database:** Use strong passwords and enable SSL for database connections

## 📞 Support

For deployment issues or questions, contact the development team.

**Deployment Paths:**
- Backend: `/var/www/nirvana-backend`
- Frontend: `/var/www/nirvana-frontend`
- Logs: `/var/log/pm2/` and `/var/log/deploy/`
