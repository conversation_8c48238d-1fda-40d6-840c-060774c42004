// Nirvana Organics - Production PM2 Ecosystem Configuration
module.exports = {
  apps: [
    {
      name: 'nirvana-main-server',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend',
      instances: 'max',
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      env: {
        NODE_ENV: 'production',
        PORT: 5000,
        PM2_SERVE_PATH: './dist',
        PM2_SERVE_PORT: 5000,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/index.html'
      },
      log_file: '/var/log/pm2/nirvana-main-combined.log',
      out_file: '/var/log/pm2/nirvana-main-out.log',
      error_file: '/var/log/pm2/nirvana-main-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      kill_timeout: 5000,
      listen_timeout: 8000,
      shutdown_with_message: true
    },
    {
      name: 'nirvana-admin-server',
      script: './server/admin-server.js',
      cwd: '/var/www/nirvana-backend',
      instances: 2,
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
        PM2_SERVE_PATH: './dist-admin',
        PM2_SERVE_PORT: 3001,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/admin.html'
      },
      log_file: '/var/log/pm2/nirvana-admin-combined.log',
      out_file: '/var/log/pm2/nirvana-admin-out.log',
      error_file: '/var/log/pm2/nirvana-admin-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      kill_timeout: 5000,
      listen_timeout: 8000,
      shutdown_with_message: true
    }
  ],

  deploy: {
    production: {
      user: 'deploy',
      host: ['your-production-server-ip'],
      ref: 'origin/main',
      repo: '**************:your-username/nirvana-organics.git',
      path: '/var/www/nirvana-backend',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && npm run build:admin && pm2 reload ecosystem.config.production.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};