// Load environment configuration based on NODE_ENV
const envFile = process.env.NODE_ENV === 'testing' ? '.env.admin.test' :
                process.env.NODE_ENV === 'production' ? '.env.admin' : '.env.admin';
require('dotenv').config({ path: envFile });
console.log(`🔧 Admin server loading environment from: ${envFile}`);
console.log(`🌍 NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`🔌 PORT: ${process.env.PORT}`);
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
const { sequelize } = require('./models');

const app = express();

// Admin-specific middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'", "https://admin.shopnirvanaorganics.com"],
      frameAncestors: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Admin-specific CORS
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['https://admin.shopnirvanaorganics.com'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Admin-specific rate limiting (stricter)
const adminRateLimit = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 300000, // 5 minutes
  max: parseInt(process.env.ADMIN_RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 300000) / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/api/health';
  }
});

app.use('/api', adminRateLimit);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Admin-specific logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  const ip = req.ip || req.connection.remoteAddress;
  const userAgent = req.get('User-Agent') || 'Unknown';
  
  console.log(`[ADMIN] ${timestamp} - ${req.method} ${req.path} - IP: ${ip} - UA: ${userAgent}`);
  
  // Log admin actions for audit trail
  if (req.path.startsWith('/api/admin') && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method)) {
    console.log(`[ADMIN_ACTION] ${timestamp} - ${req.method} ${req.path} - IP: ${ip} - Body:`, 
      JSON.stringify(req.body, null, 2));
  }
  
  next();
});

// IP Whitelist middleware (if configured)
if (process.env.ADMIN_IP_WHITELIST) {
  const allowedIPs = process.env.ADMIN_IP_WHITELIST.split(',').map(ip => ip.trim());
  
  app.use((req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (!allowedIPs.includes(clientIP)) {
      console.warn(`[ADMIN_SECURITY] Blocked access from unauthorized IP: ${clientIP}`);
      return res.status(403).json({ 
        error: 'Access denied from this IP address',
        code: 'IP_NOT_WHITELISTED'
      });
    }
    
    next();
  });
}

// Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/admin', require('./routes/admin'));
app.use('/api/products', require('./routes/products'));
app.use('/api/categories', require('./routes/categories'));
app.use('/api/orders', require('./routes/orders'));
app.use('/api/users', require('./routes/users'));
app.use('/api/analytics', require('./routes/analytics'));
app.use('/api/upload', require('./routes/upload'));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    mode: 'admin',
    version: '1.0.0',
    uptime: process.uptime(),
    database: sequelize.authenticate() ? 'connected' : 'disconnected'
  });
});

// Serve static files from admin build
app.use(express.static(path.join(__dirname, '../dist-admin')));

// Admin SPA fallback
app.get('*', (req, res) => {
  // Don't serve SPA for API routes
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found' });
  }
  
  res.sendFile(path.join(__dirname, '../dist-admin/index.html'));
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('[ADMIN_ERROR]', err);
  
  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(err.status || 500).json({
    error: isDevelopment ? err.message : 'Internal server error',
    code: err.code || 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
    ...(isDevelopment && { stack: err.stack })
  });
});

// Database connection
sequelize.authenticate()
  .then(() => {
    console.log('✅ Admin panel database connection established successfully.');
  })
  .catch(err => {
    console.error('❌ Unable to connect to the database:', err);
  });

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`🚀 Nirvana Organics Admin Panel server running on port ${PORT}`);
  console.log(`📊 Admin panel available at: ${process.env.FRONTEND_URL || `http://localhost:${PORT}`}`);
  console.log(`🔒 Security mode: ${process.env.ADMIN_SECURITY_MODE ? 'Enhanced' : 'Standard'}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
});

module.exports = app;
