# Nirvana Organics - Test Environment Deployment

This folder contains the complete test deployment package for Nirvana Organics e-commerce platform.

## 📁 Folder Contents

```
nirvana-test/
├── server/                          # Complete backend application
│   ├── index.js                     # Main server entry point
│   ├── admin-server.js              # Admin server entry point
│   ├── config/                      # Configuration files
│   ├── controllers/                 # API controllers
│   ├── middleware/                  # Express middleware
│   ├── models/                      # Database models
│   ├── routes/                      # API routes
│   ├── services/                    # Business logic services
│   ├── utils/                       # Utility functions
│   ├── migrations/                  # Database migrations
│   ├── seeders/                     # Database seeders
│   └── templates/                   # Email templates
├── dist/                            # Main frontend build (test)
├── dist-admin/                      # Admin frontend build (test)
├── .env.test                        # Main server environment config
├── .env.admin.test                  # Admin server environment config
├── ecosystem.config.test.js         # PM2 configuration
├── package.json                     # Node.js dependencies
├── package-lock.json               # Dependency lock file
├── deploy.sh                        # Deployment script
└── README.md                        # This file
```

## 🚀 Deployment Instructions

### Prerequisites

1. **Server Requirements:**
   - Ubuntu 20.04+ or CentOS 8+
   - Node.js 18+ and npm 9+
   - PM2 process manager
   - Nginx web server
   - SSL certificate for test.shopnirvanaorganics.com

2. **Database:**
   - MySQL 8.0+ or MariaDB 10.6+
   - Database and user credentials configured

3. **Domain Configuration:**
   - `test.shopnirvanaorganics.com` → Main test site
   - `test.shopnirvanaorganics.com/admin` → Admin panel (path-based)

### Step 1: Configure Environment Variables

**Edit `.env.test` (if needed):**
```bash
# Most values are pre-configured for test environment
# Update database credentials if different:
DB_HOST=srv1921.hstgr.io
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912

# Update API keys for test environment:
SQUARE_ACCESS_TOKEN=your-test-square-access-token
GOOGLE_CLIENT_SECRET=your-test-google-client-secret
```

**Edit `.env.admin.test` (if needed):**
```bash
# Most values are pre-configured for test environment
# Update database credentials if different:
DB_HOST=srv1921.hstgr.io
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
```

### Step 2: Upload to Server

```bash
# Upload the entire nirvana-test folder to your server
scp -r nirvana-test/ user@your-server:/tmp/

# Or use rsync for better performance
rsync -avz --progress nirvana-test/ user@your-server:/tmp/nirvana-test/
```

### Step 3: Run Deployment Script

```bash
# SSH into your server
ssh user@your-server

# Navigate to deployment folder
cd /tmp/nirvana-test

# Make deployment script executable
chmod +x deploy.sh

# Run deployment (requires sudo access)
sudo ./deploy.sh
```

### Step 4: Configure Nginx

Create nginx configuration for test environment:

```nginx
# /etc/nginx/sites-available/nirvana-organics-test
server {
    listen 80;
    server_name test.shopnirvanaorganics.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name test.shopnirvanaorganics.com;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    root /var/www/nirvana-frontend-test/main;
    index index.html;
    
    # Admin panel (path-based routing)
    location /admin {
        alias /var/www/nirvana-frontend-test/admin;
        try_files $uri $uri/ /admin/admin.html;
        
        # Admin API proxy
        location /admin/api/ {
            proxy_pass http://localhost:3001/api/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
    }
    
    # Main API proxy to backend
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Main frontend SPA fallback
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

Enable the configuration:
```bash
sudo ln -s /etc/nginx/sites-available/nirvana-organics-test /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔍 Verification

After deployment, verify everything is working:

```bash
# Check PM2 processes
sudo -u deploy pm2 status

# Check logs
sudo -u deploy pm2 logs

# Test health endpoints
curl https://test.shopnirvanaorganics.com/api/health
curl https://test.shopnirvanaorganics.com/admin/api/health

# Test frontend
curl -I https://test.shopnirvanaorganics.com
curl -I https://test.shopnirvanaorganics.com/admin
```

## 🔧 Management Commands

```bash
# Restart services
sudo -u deploy pm2 restart nirvana-backend-main-test nirvana-backend-admin-test

# View logs
sudo -u deploy pm2 logs

# Monitor processes
sudo -u deploy pm2 monit

# Stop services
sudo -u deploy pm2 stop nirvana-backend-main-test nirvana-backend-admin-test

# Start services
sudo -u deploy pm2 start ecosystem.config.test.js --env test
```

## 🧪 Testing Features

This test environment includes:

1. **Debug Mode:** Enhanced logging and error reporting
2. **Source Maps:** For easier debugging of frontend issues
3. **Test Database:** Separate from production data
4. **Sandbox APIs:** Square payments in sandbox mode
5. **Development Tools:** Additional debugging endpoints

## 🛡️ Security Notes

1. **Test Environment:** This is for testing only - not for production use
2. **Database:** Uses test database with sample data
3. **API Keys:** Uses sandbox/test API keys
4. **SSL/TLS:** Still requires HTTPS for proper testing
5. **Access:** Can be more permissive than production for testing

## 📞 Support

For deployment issues or questions, contact the development team.

**Deployment Paths:**
- Backend: `/var/www/nirvana-backend-test`
- Frontend: `/var/www/nirvana-frontend-test`
- Logs: `/var/log/pm2/` and `/var/log/deploy/`

## 🔄 Deployment Workflow

1. **Test First:** Always deploy to test environment first
2. **Validate:** Thoroughly test all functionality
3. **Production:** Only deploy to production after test validation
4. **Rollback:** Keep backups for quick rollback if needed
