// Nirvana Organics - Test Environment PM2 Ecosystem Configuration
module.exports = {
  apps: [
    {
      name: 'nirvana-main-server-test',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend-test',
      instances: 2,
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      min_uptime: '10s',
      max_restarts: 15,
      restart_delay: 2000,
      env: {
        NODE_ENV: 'testing',
        PORT: 5000,
        PM2_SERVE_PATH: './dist',
        PM2_SERVE_PORT: 5000,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/index.html'
      },
      log_file: '/var/log/pm2/nirvana-main-test-combined.log',
      out_file: '/var/log/pm2/nirvana-main-test-out.log',
      error_file: '/var/log/pm2/nirvana-main-test-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      kill_timeout: 3000,
      listen_timeout: 5000,
      shutdown_with_message: true
    },
    {
      name: 'nirvana-admin-server-test',
      script: './server/admin-server.js',
      cwd: '/var/www/nirvana-backend-test',
      instances: 1,
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '256M',
      min_uptime: '10s',
      max_restarts: 15,
      restart_delay: 2000,
      env: {
        NODE_ENV: 'testing',
        PORT: 3001,
        PM2_SERVE_PATH: './dist-admin',
        PM2_SERVE_PORT: 3001,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/admin.html'
      },
      log_file: '/var/log/pm2/nirvana-admin-test-combined.log',
      out_file: '/var/log/pm2/nirvana-admin-test-out.log',
      error_file: '/var/log/pm2/nirvana-admin-test-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      kill_timeout: 3000,
      listen_timeout: 5000,
      shutdown_with_message: true
    }
  ],
  
  deploy: {
    test: {
      user: 'deploy',
      host: ['your-test-server-ip'],
      ref: 'origin/develop',
      repo: '**************:your-username/nirvana-organics.git',
      path: '/var/www/nirvana-backend-test',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build:test && npm run build:admin:test && pm2 reload ecosystem.config.test.js --env test',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
