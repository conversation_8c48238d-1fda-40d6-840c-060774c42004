#!/usr/bin/env node

/**
 * Nirvana Organics - Deployment Verification Script
 * Verifies that both production and test deployment folders are correctly configured
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    success(`${description} exists`);
    return true;
  } else {
    error(`${description} missing: ${filePath}`);
    return false;
  }
}

function checkDirectoryExists(dirPath, description) {
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    success(`${description} directory exists`);
    return true;
  } else {
    error(`${description} directory missing: ${dirPath}`);
    return false;
  }
}

function verifyEnvironmentFile(filePath, requiredVars) {
  if (!fs.existsSync(filePath)) {
    error(`Environment file missing: ${filePath}`);
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  let allVarsPresent = true;

  for (const varName of requiredVars) {
    if (content.includes(`${varName}=`)) {
      success(`${varName} configured in ${path.basename(filePath)}`);
    } else {
      error(`${varName} missing in ${path.basename(filePath)}`);
      allVarsPresent = false;
    }
  }

  return allVarsPresent;
}

function verifyServerFiles(deploymentPath) {
  const serverPath = path.join(deploymentPath, 'server');
  
  // Check main server files
  const mainFiles = [
    'index.js',
    'admin-server.js'
  ];

  let allFilesExist = true;
  for (const file of mainFiles) {
    if (!checkFileExists(path.join(serverPath, file), `Server ${file}`)) {
      allFilesExist = false;
    }
  }

  // Check server directories
  const serverDirs = [
    'config',
    'controllers', 
    'middleware',
    'models',
    'routes',
    'services',
    'utils'
  ];

  for (const dir of serverDirs) {
    if (!checkDirectoryExists(path.join(serverPath, dir), `Server ${dir}`)) {
      allFilesExist = false;
    }
  }

  return allFilesExist;
}

function verifyFrontendBuilds(deploymentPath) {
  const distPath = path.join(deploymentPath, 'dist');
  const distAdminPath = path.join(deploymentPath, 'dist-admin');

  let buildsExist = true;

  // Check main frontend build
  if (checkDirectoryExists(distPath, 'Main frontend build (dist)')) {
    if (!checkFileExists(path.join(distPath, 'index.html'), 'Main frontend index.html')) {
      buildsExist = false;
    }
  } else {
    buildsExist = false;
  }

  // Check admin frontend build
  if (checkDirectoryExists(distAdminPath, 'Admin frontend build (dist-admin)')) {
    if (!checkFileExists(path.join(distAdminPath, 'admin.html'), 'Admin frontend admin.html')) {
      buildsExist = false;
    }
  } else {
    buildsExist = false;
  }

  return buildsExist;
}

function verifyDeployment(deploymentName, deploymentPath, envFiles, requiredVars) {
  log(`\n🔍 Verifying ${deploymentName} deployment...`, 'cyan');
  
  let isValid = true;

  // Check if deployment directory exists
  if (!checkDirectoryExists(deploymentPath, `${deploymentName} deployment`)) {
    return false;
  }

  // Check core files
  const coreFiles = [
    'package.json',
    'package-lock.json'
  ];

  for (const file of coreFiles) {
    if (!checkFileExists(path.join(deploymentPath, file), file)) {
      isValid = false;
    }
  }

  // Check environment files
  for (const envFile of envFiles) {
    if (!verifyEnvironmentFile(path.join(deploymentPath, envFile), requiredVars)) {
      isValid = false;
    }
  }

  // Check server files
  if (!verifyServerFiles(deploymentPath)) {
    isValid = false;
  }

  // Check frontend builds
  if (!verifyFrontendBuilds(deploymentPath)) {
    isValid = false;
  }

  // Check deployment script
  if (!checkFileExists(path.join(deploymentPath, 'deploy.sh'), 'Deployment script')) {
    isValid = false;
  }

  // Check README
  if (!checkFileExists(path.join(deploymentPath, 'README.md'), 'README documentation')) {
    isValid = false;
  }

  return isValid;
}

function main() {
  log('🚀 Nirvana Organics - Deployment Verification', 'magenta');
  log('================================================', 'magenta');

  const requiredEnvVars = [
    'NODE_ENV',
    'PORT',
    'DB_HOST',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'JWT_SECRET',
    'FRONTEND_URL',
    'BACKEND_URL'
  ];

  // Verify production deployment
  const productionValid = verifyDeployment(
    'Production',
    './nirvana-production',
    ['.env.production', '.env.admin'],
    requiredEnvVars
  );

  // Verify test deployment
  const testValid = verifyDeployment(
    'Test',
    './nirvana-test', 
    ['.env.test', '.env.admin.test'],
    requiredEnvVars
  );

  // Summary
  log('\n📊 Verification Summary', 'cyan');
  log('======================', 'cyan');

  if (productionValid) {
    success('Production deployment is ready for deployment');
  } else {
    error('Production deployment has issues that need to be resolved');
  }

  if (testValid) {
    success('Test deployment is ready for deployment');
  } else {
    error('Test deployment has issues that need to be resolved');
  }

  if (productionValid && testValid) {
    log('\n🎉 All deployments are ready!', 'green');
    log('Next steps:', 'blue');
    log('1. Upload nirvana-test/ to your test server and run ./deploy.sh', 'blue');
    log('2. Test thoroughly on test environment', 'blue');
    log('3. Upload nirvana-production/ to your production server and run ./deploy.sh', 'blue');
    process.exit(0);
  } else {
    log('\n❌ Some deployments have issues. Please fix them before deploying.', 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
