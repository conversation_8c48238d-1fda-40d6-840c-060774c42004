#!/bin/bash

# Verification Script for Nirvana Organics Test Environment Fix
# Checks if all three critical issues have been resolved

echo "🔍 Verifying Nirvana Organics Test Environment Fix"
echo "=================================================="

DEPLOY_DIR="/var/www/nirvana-backend-test"
DEPLOY_USER="Nirvana"

# Check if environment files exist and have correct values
echo "📁 Environment Files Check:"
echo "==========================="

if [ -f "$DEPLOY_DIR/.env.test" ]; then
    echo "✅ .env.test exists"
    echo "   NODE_ENV: $(grep '^NODE_ENV=' $DEPLOY_DIR/.env.test)"
    echo "   DB_HOST: $(grep '^DB_HOST=' $DEPLOY_DIR/.env.test)"
    echo "   SQUARE_ACCESS_TOKEN: $(grep '^SQUARE_ACCESS_TOKEN=' $DEPLOY_DIR/.env.test | cut -c1-30)..."
else
    echo "❌ .env.test missing"
fi

if [ -f "$DEPLOY_DIR/.env.admin.test" ]; then
    echo "✅ .env.admin.test exists"
    echo "   NODE_ENV: $(grep '^NODE_ENV=' $DEPLOY_DIR/.env.admin.test)"
    echo "   DB_HOST: $(grep '^DB_HOST=' $DEPLOY_DIR/.env.admin.test)"
    echo "   SQUARE_ACCESS_TOKEN: $(grep '^SQUARE_ACCESS_TOKEN=' $DEPLOY_DIR/.env.admin.test | cut -c1-30)..."
else
    echo "❌ .env.admin.test missing"
fi

echo ""

# Check PM2 process status
echo "📊 PM2 Process Status:"
echo "====================="
sudo -u $DEPLOY_USER pm2 status | grep nirvana-backend

echo ""

# Test environment variable loading manually
echo "🧪 Manual Environment Loading Test:"
echo "==================================="
cd $DEPLOY_DIR

echo "Testing .env.test:"
NODE_ENV=test node -e "
require('dotenv').config({ path: '.env.test' });
console.log('✅ NODE_ENV:', process.env.NODE_ENV);
console.log('✅ DB_HOST:', process.env.DB_HOST);
console.log('✅ DB_PORT:', process.env.DB_PORT);
console.log('✅ SQUARE_ACCESS_TOKEN:', process.env.SQUARE_ACCESS_TOKEN ? 'CONFIGURED' : '❌ MISSING');
console.log('✅ EMAIL_USER:', process.env.EMAIL_USER);
" 2>/dev/null || echo "❌ Failed to load .env.test"

echo ""
echo "Testing .env.admin.test:"
NODE_ENV=test node -e "
require('dotenv').config({ path: '.env.admin.test' });
console.log('✅ NODE_ENV:', process.env.NODE_ENV);
console.log('✅ DB_HOST:', process.env.DB_HOST);
console.log('✅ DB_PORT:', process.env.DB_PORT);
console.log('✅ SQUARE_ACCESS_TOKEN:', process.env.SQUARE_ACCESS_TOKEN ? 'CONFIGURED' : '❌ MISSING');
console.log('✅ EMAIL_USER:', process.env.EMAIL_USER);
" 2>/dev/null || echo "❌ Failed to load .env.admin.test"

echo ""

# Check recent logs for error patterns
echo "🔍 Error Pattern Analysis:"
echo "=========================="

echo "Checking for database connection errors (::1:3306):"
if sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -q "::1:3306"; then
    echo "❌ Still connecting to IPv6 localhost (::1:3306)"
    echo "   Recent occurrences:"
    sudo -u $DEPLOY_USER pm2 logs --lines 20 | grep "::1:3306" | tail -3
else
    echo "✅ No IPv6 localhost connection errors found"
fi

echo ""
echo "Checking for email authentication errors:"
if sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -q "Missing credentials for.*PLAIN"; then
    echo "❌ Still experiencing email authentication errors"
    echo "   Recent occurrences:"
    sudo -u $DEPLOY_USER pm2 logs --lines 20 | grep "Missing credentials" | tail -3
else
    echo "✅ No email authentication errors found"
fi

echo ""
echo "Checking for Square Access Token errors:"
if sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -q "Square Access Token not configured"; then
    echo "❌ Square Access Token still not configured"
    echo "   Recent occurrences:"
    sudo -u $DEPLOY_USER pm2 logs --lines 20 | grep "Square Access Token" | tail -3
else
    echo "✅ No Square Access Token errors found"
fi

echo ""

# Check for success indicators
echo "✅ Success Indicators Check:"
echo "============================"

echo "Looking for mock email transporter messages:"
if sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -q "Mock.*email.*transporter"; then
    echo "✅ Mock email transporters are active"
    sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep "Mock.*email.*transporter" | tail -3
else
    echo "⚠️  Mock email transporter messages not found"
fi

echo ""
echo "Looking for database connection success:"
if sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -q -E "(Database connected|Connection has been established successfully)"; then
    echo "✅ Database connection successful"
    sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -E "(Database connected|Connection has been established successfully)" | tail -2
else
    echo "⚠️  Database connection success messages not found"
fi

echo ""
echo "Looking for Square SDK initialization:"
if sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -q "Square SDK initialized"; then
    echo "✅ Square SDK initialized successfully"
    sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep "Square SDK initialized" | tail -2
else
    echo "⚠️  Square SDK initialization messages not found"
fi

echo ""

# Health check endpoints
echo "🏥 Health Check Endpoints:"
echo "========================="

echo "Testing main server health endpoint:"
if curl -s -f https://test.shopnirvanaorganics.com/api/health >/dev/null 2>&1; then
    echo "✅ Main server health endpoint responding"
else
    echo "❌ Main server health endpoint not responding"
fi

echo "Testing admin server health endpoint:"
if curl -s -f https://test.shopnirvanaorganics.com/admin/api/health >/dev/null 2>&1; then
    echo "✅ Admin server health endpoint responding"
else
    echo "❌ Admin server health endpoint not responding"
fi

echo ""

# Summary
echo "📋 Fix Summary:"
echo "==============="

# Count issues
ISSUES=0

if sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -q "::1:3306"; then
    echo "❌ Database connection issue: UNRESOLVED"
    ((ISSUES++))
else
    echo "✅ Database connection issue: RESOLVED"
fi

if sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -q "Missing credentials for.*PLAIN"; then
    echo "❌ Email authentication issue: UNRESOLVED"
    ((ISSUES++))
else
    echo "✅ Email authentication issue: RESOLVED"
fi

if sudo -u $DEPLOY_USER pm2 logs --lines 50 | grep -q "Square Access Token not configured"; then
    echo "❌ Square configuration issue: UNRESOLVED"
    ((ISSUES++))
else
    echo "✅ Square configuration issue: RESOLVED"
fi

echo ""
if [ $ISSUES -eq 0 ]; then
    echo "🎉 ALL ISSUES RESOLVED! Your test environment is working correctly."
else
    echo "⚠️  $ISSUES issue(s) still need attention. Check the error details above."
    echo ""
    echo "🔧 If issues persist, try:"
    echo "1. sudo -u $DEPLOY_USER pm2 restart all"
    echo "2. Check PM2 logs: sudo -u $DEPLOY_USER pm2 logs --lines 30"
    echo "3. Verify environment files are readable: ls -la $DEPLOY_DIR/.env*"
fi
